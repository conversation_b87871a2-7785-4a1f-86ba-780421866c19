<p-dialog [header]="title" [modal]="true" [(visible)]="visible"
  [style]="{width: '50vw', 'max-width':'800px'}"
  [breakpoints]="{'960px': '75vw', '640px': '95vw'}"
  [closable]="false" [draggable]="false" [resizable]="false">

  <div class="flex flex-col gap-4 p-4 text-md">
    <!-- Customer Information -->
    <div class="p-4 mb-4 border border-gray-200 rounded-lg bg-gray-50">
      <h6 class="mb-3 text-lg font-semibold text-gray-800">Customer Information</h6>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <p-floatlabel variant="on" styleClass="w-full">
          <input pInputText id="customerName" [(ngModel)]="customerName"
            [disabled]="isProcessing" autocomplete="off" class="w-full" />
          <label for="customerName">Customer Name</label>
        </p-floatlabel>
        <p-floatlabel variant="on" styleClass="w-full">
          <input pInputText id="customerPhone" [(ngModel)]="customerPhone"
            [disabled]="isProcessing" autocomplete="off" class="w-full" />
          <label for="customerPhone">Phone Number</label>
        </p-floatlabel>
      </div>
    </div>
    <!-- Payment Methods -->
    <div>
      <h6 class="mb-3 text-lg font-semibold text-gray-800">Select Payment Method</h6>
      <div class="grid grid-cols-2 gap-3 mb-6 lg:grid-cols-4">
        <div *ngFor="let method of paymentMethods; trackBy: trackByMethod"
             (click)="handlePaymentMethodChange(method.value)"
             class="p-3 transition-all duration-200 border-2 rounded-lg cursor-pointer hover:shadow-md"
             [class]="paymentData.selectedPaymentMethod === method.value ?
               'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
             [class.opacity-60]="isProcessing">
          <div class="flex flex-col items-center justify-center min-h-[70px]">
            <i [class]="method.icon + ' text-2xl mb-2'"
               [ngClass]="paymentData.selectedPaymentMethod === method.value ? 'text-blue-600' : 'text-gray-600'"></i>
            <span class="text-sm font-medium text-center"
                  [ngClass]="paymentData.selectedPaymentMethod === method.value ? 'text-blue-700' : 'text-gray-700'">
              {{method.label}}
            </span>
          </div>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="mb-6" *ngIf="getSelectedMethod() as method">
        <div class="p-4 border rounded-lg" [ngClass]="getClasses(method.color, 'detail')">
          <div class="flex items-center gap-3 mb-4">
            <i [class]="method.icon + ' ' + getClasses(method.color, 'icon')"></i>
            <h4 class="text-lg font-semibold" [ngClass]="getClasses(method.color, 'label')">
              {{method.label}} Payment
            </h4>
          </div>

          <div class="space-y-4">
            <div *ngFor="let field of method.fields" class="mb-3">
              <!-- Amount Field -->
              <ng-container *ngIf="field.type === 'amount'">
                <label class="block mb-1 text-sm font-medium text-gray-700">{{field.label}}</label>
                <input pInputText #amountInput type="number" [value]="paymentData[field.key] || ''"
                  (input)="onAmountInput(amountInput, field.key)" [min]="field.min" [max]="field.max"
                  [disabled]="isProcessing" class="w-full p-2 border rounded" step="0.01" autocomplete="off">
              </ng-container>

              <!-- Text Field -->
              <ng-container *ngIf="field.type === 'text'">
                <label class="block mb-1 text-sm font-medium text-gray-700">{{field.label}}</label>
                <input type="text" pInputText [placeholder]="field.placeholder || ''"
                  [(ngModel)]="paymentData[field.key]" [disabled]="isProcessing" class="w-full"
                  [ngClass]="{'p-invalid': field.required && !paymentData[field.key]}">
              </ng-container>

              <!-- Radio Field -->
              <ng-container *ngIf="field.type === 'radio'">
                <label class="block mb-2 text-sm font-medium text-gray-700">{{field.label}}</label>
                <div class="flex items-center gap-6">
                  <div *ngFor="let option of field.options" class="flex items-center gap-2">
                    <p-radioButton [name]="field.key" [value]="option.value" [(ngModel)]="paymentData[field.key]"
                      [disabled]="isProcessing" [inputId]="field.key + option.value"></p-radioButton>
                    <label [for]="field.key + option.value" class="cursor-pointer">{{option.label}}</label>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Summary -->
    <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div class="grid grid-cols-1 gap-2 text-2xl">
        <div class="flex justify-between">
          <span class="font-bold text-gray-800">Total Amount:</span>
          <span class="font-bold">{{ totalAmount | currency:'INR' }}</span>
        </div>
        <div class="flex justify-between text-lg text-gray-600">
          <span>Remaining Amount:</span>
          <span>{{ remainingAmount | currency:'INR' }}</span>
        </div>
        <div *ngIf="change > 0" class="flex justify-between text-lg text-green-600">
          <span>Change:</span>
          <span>{{ change | currency:'INR' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex justify-end w-full gap-2">
      <button pButton pRipple type="button" [label]="cancelButtonLabel"
        class="p-button-text" [disabled]="isProcessing" (click)="onCancel()"></button>
      <button pButton pRipple type="button" [label]="confirmButtonLabel"
        [disabled]="isProcessing || remainingAmount > 0" [loading]="isProcessing" (click)="onConfirm()"></button>
    </div>
  </ng-template>
</p-dialog>