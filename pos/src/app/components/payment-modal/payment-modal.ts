import { ChangeDetectionStrategy, Component, computed, effect, input, model, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FloatLabelModule } from 'primeng/floatlabel';

import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput } from '../../services/payment.config.service';

@Component({
  selector: 'app-payment-modal',
  standalone: true,
  templateUrl: './payment-modal.html',
  imports: [CommonModule, FormsModule, DialogModule, RadioButtonModule, ButtonModule, InputTextModule, FloatLabelModule],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent {
  // Modern Angular signals for inputs/outputs
  visible = model(false);
  totalAmount = input(0);
  isProcessing = input(false);
  title = input('Complete Payment');
  confirmButtonLabel = input('Confirm Payment');
  cancelButtonLabel = input('Cancel');

  cancel = output<void>();
  confirm = output<PaymentModalOutput>();

  // Internal state as signals
  paymentData = signal<PaymentData>({ selectedPaymentMethod: 'cash' });
  customerName = signal('');
  customerPhone = signal('');

  // Computed values using modern signals
  paymentMethods = computed(() =>
    this.paymentService.getPaymentMethods(this.totalAmount())
  );

  selectedMethod = computed(() =>
    this.paymentMethods().find(m => m.value === this.paymentData().selectedPaymentMethod)
  );

  amounts = computed(() =>
    this.paymentService.calculateAmounts(this.paymentData(), this.totalAmount())
  );

  remainingAmount = computed(() => this.amounts().remaining);
  change = computed(() => this.amounts().change);

  canConfirm = computed(() =>
    !this.isProcessing() && this.remainingAmount() <= 0
  );

  constructor(private paymentService: PaymentService) {
    // Auto-reset when modal opens
    effect(() => {
      if (this.visible()) {
        this.resetPaymentData();
      }
    });
  }

  trackByMethod = (_: number, m: PaymentMethod) => m.value;

  private resetPaymentData(): void {
    this.paymentData.set(this.paymentService.initializePaymentData(this.totalAmount()));
    this.customerName.set('');
    this.customerPhone.set('');
  }

  selectPaymentMethod(method: string): void {
    if (!this.isProcessing()) {
      this.paymentData.update(data => ({ ...data, selectedPaymentMethod: method }));
    }
  }

  updatePaymentField(fieldKey: string, value: number): void {
    this.paymentData.update(data => ({ ...data, [fieldKey]: value }));
  }

  onConfirm(): void {
    if (this.isProcessing()) return;

    const isValid = this.paymentService.validatePayment(
      this.paymentData(),
      this.customerName(),
      this.customerPhone(),
      this.totalAmount()
    );

    if (isValid) {
      this.confirm.emit({
        paymentMethod: this.paymentData().selectedPaymentMethod,
        paymentData: this.paymentData(),
        customerName: this.customerName(),
        customerPhone: this.customerPhone()
      });
    }
  }

  onCancel(): void {
    if (!this.isProcessing()) {
      this.visible.set(false);
      this.cancel.emit();
    }
  }

  getClasses = (color: string, type: 'detail' | 'icon' | 'label') =>
    this.paymentService.getStyleClasses(color, type);

  // Helper for template
  parseFloat = parseFloat;
}